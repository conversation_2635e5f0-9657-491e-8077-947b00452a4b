<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beautiful Banner Slideshow</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
        }

        .slideshow-container {
            position: relative;
            width: 100vw;
            margin: 0;
            overflow: hidden;
        }

        .slides-wrapper {
            display: flex;
            width: 400%; /* 4 slides × 100% = 400% */
            height: 600px;
            transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .slide {
            width: 25%; /* Each slide takes 25% of the wrapper (100% / 4 slides) */
            height: 100%;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .slide:hover img {
            transform: scale(1.05);
        }

        .slide img:hover {
            filter: brightness(1.1);
        }

        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 24px;
            color: #333;
            transition: all 0.3s ease;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .nav-button:hover {
            background: white;
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .nav-button:active {
            transform: translateY(-50%) scale(0.95);
        }

        .prev {
            left: 20px;
        }

        .next {
            right: 20px;
        }

        .dots-container {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.9);
        }

        .dot {
            height: 15px;
            width: 15px;
            margin: 0 5px;
            background-color: #bbb;
            border-radius: 50%;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dot:hover {
            background-color: #717171;
            transform: scale(1.2);
        }

        .dot.active {
            background-color: #667eea;
            transform: scale(1.3);
        }

        .slide-counter {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10;
        }

        @media (max-width: 768px) {
            .slide {
                height: 300px;
            }

            .nav-button {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .prev {
                left: 10px;
            }

            .next {
                right: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">4</span>
        </div>

        <div class="slides-wrapper" id="slides-wrapper">
            <div class="slide">
                <img src="../public/banners/banner-image-1.png" alt="Banner 1" onclick="handleImageClick(1)">
            </div>
            <div class="slide">
                <img src="../public/banners/banner-image-2.png" alt="Banner 2" onclick="handleImageClick(2)">
            </div>
            <div class="slide">
                <img src="../public/banners/banner-image-3.png" alt="Banner 3" onclick="handleImageClick(3)">
            </div>
            <div class="slide">
                <img src="../public/banners/banner-image-4.png" alt="Banner 4" onclick="handleImageClick(4)">
            </div>
        </div>

        <button class="nav-button prev" onclick="changeSlide(-1)">❮</button>
        <button class="nav-button next" onclick="changeSlide(1)">❯</button>

        <div class="dots-container">
            <span class="dot active" onclick="currentSlide(1)"></span>
            <span class="dot" onclick="currentSlide(2)"></span>
            <span class="dot" onclick="currentSlide(3)"></span>
            <span class="dot" onclick="currentSlide(4)"></span>
        </div>
    </div>

    <script>
        let slideIndex = 1;
        const totalSlides = 4;

        function changeSlide(direction) {
            slideIndex += direction;

            if (slideIndex > totalSlides) {
                slideIndex = 1;
            }
            if (slideIndex < 1) {
                slideIndex = totalSlides;
            }

            showSlide(slideIndex);
            restartAutoSlide(); // Restart auto-slide timer when user manually navigates
        }

        function currentSlide(index) {
            slideIndex = index;
            showSlide(slideIndex);
            restartAutoSlide(); // Restart auto-slide timer when user clicks dots
        }

        function showSlide(index) {
            const slides = document.querySelectorAll('.slide');
            const dots = document.querySelectorAll('.dot');

            // Hide all slides
            slides.forEach(slide => slide.classList.remove('active'));

            // Remove active class from all dots
            dots.forEach(dot => dot.classList.remove('active'));

            // Show current slide
            slides[index - 1].classList.add('active');

            // Activate current dot
            dots[index - 1].classList.add('active');

            // Update counter
            document.getElementById('current-slide').textContent = index;
        }

        // Handle image clicks
        function handleImageClick(imageNumber) {
            stopAutoSlide(); // Pause auto-slide when user clicks an image

            // You can customize what happens when each image is clicked
            switch(imageNumber) {
                case 1:
                    alert('Banner 1 clicked! You can redirect to a specific page or product.');
                    // Example: window.location.href = '/products/collection1';
                    break;
                case 2:
                    alert('Banner 2 clicked! Add your custom action here.');
                    // Example: window.location.href = '/products/collection2';
                    break;
                case 3:
                    alert('Banner 3 clicked! Add your custom action here.');
                    // Example: window.location.href = '/products/collection3';
                    break;
                case 4:
                    alert('Banner 4 clicked! Add your custom action here.');
                    // Example: window.location.href = '/products/collection4';
                    break;
                default:
                    console.log('Image clicked:', imageNumber);
            }

            // Resume auto-slide after 3 seconds
            setTimeout(startAutoSlide, 3000);
        }

        // Auto-slide functionality
        let autoSlideInterval;

        function autoSlide() {
            changeSlide(1);
        }

        function startAutoSlide() {
            autoSlideInterval = setInterval(autoSlide, 4000); // Auto-slide every 4 seconds
        }

        function stopAutoSlide() {
            clearInterval(autoSlideInterval);
        }

        function restartAutoSlide() {
            stopAutoSlide();
            startAutoSlide();
        }

        // Start auto-sliding when page loads
        startAutoSlide();

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft') {
                changeSlide(-1);
            } else if (event.key === 'ArrowRight') {
                changeSlide(1);
            }
        });

        // Touch/swipe support for mobile
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', function(event) {
            startX = event.touches[0].clientX;
        });

        document.addEventListener('touchend', function(event) {
            endX = event.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swiped left - next slide
                    changeSlide(1);
                } else {
                    // Swiped right - previous slide
                    changeSlide(-1);
                }
            }
        }
    </script>
</body>
</html>