import React, { useState, useEffect, useRef } from 'react';
import './banner.css';

interface BannerProps {
  images?: string[];
  autoSlideInterval?: number;
  onImageClick?: (imageIndex: number) => void;
}

const Banner: React.FC<BannerProps> = ({
  images = [
    '../public/banners/banner-image-1.png',
    '../public/banners/banner-image-2.png',
    '../public/banners/banner-image-3.png',
    '../public/banners/banner-image-4.png'
  ],
  autoSlideInterval = 4000,
  onImageClick
}) => {
  const [slideIndex, setSlideIndex] = useState(1);
  const [isAutoSliding, setIsAutoSliding] = useState(true);
  const autoSlideRef = useRef<NodeJS.Timeout | null>(null);
  const totalSlides = images.length;

  // Auto-slide functionality
  const startAutoSlide = () => {
    if (autoSlideRef.current) {
      clearInterval(autoSlideRef.current);
    }
    autoSlideRef.current = setInterval(() => {
      setSlideIndex(prev => (prev >= totalSlides ? 1 : prev + 1));
    }, autoSlideInterval);
    setIsAutoSliding(true);
  };

  const stopAutoSlide = () => {
    if (autoSlideRef.current) {
      clearInterval(autoSlideRef.current);
      autoSlideRef.current = null;
    }
    setIsAutoSliding(false);
  };

  const restartAutoSlide = () => {
    stopAutoSlide();
    startAutoSlide();
  };

  // Start auto-slide on component mount
  useEffect(() => {
    startAutoSlide();
    return () => stopAutoSlide();
  }, [autoSlideInterval]);

  // Handle slide changes
  const changeSlide = (direction: number) => {
    let newIndex = slideIndex + direction;
    if (newIndex > totalSlides) newIndex = 1;
    if (newIndex < 1) newIndex = totalSlides;
    setSlideIndex(newIndex);
    restartAutoSlide();
  };

  const goToSlide = (index: number) => {
    setSlideIndex(index);
    restartAutoSlide();
  };

  // Handle image clicks
  const handleImageClick = (imageNumber: number) => {
    stopAutoSlide();
    
    if (onImageClick) {
      onImageClick(imageNumber);
    } else {
      // Default behavior
      console.log(`Banner ${imageNumber} clicked!`);
      // You can customize default actions here
    }
    
    // Resume auto-slide after 3 seconds
    setTimeout(startAutoSlide, 3000);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        changeSlide(-1);
      } else if (event.key === 'ArrowRight') {
        changeSlide(1);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [slideIndex]);

  // Touch/swipe support
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      changeSlide(1);
    } else if (isRightSwipe) {
      changeSlide(-1);
    }
  };

  return (
    <div className="slideshow-container">
      <div className="slide-counter">
        <span id="current-slide">{slideIndex}</span> / <span id="total-slides">{totalSlides}</span>
      </div>

      <div 
        className="slides-wrapper"
        style={{
          transform: `translateX(-${(slideIndex - 1) * 25}%)`
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {images.map((image, index) => (
          <div key={index} className="slide">
            <img 
              src={image} 
              alt={`Banner ${index + 1}`}
              onClick={() => handleImageClick(index + 1)}
            />
          </div>
        ))}
      </div>

      <button 
        className="nav-button prev" 
        onClick={() => changeSlide(-1)}
        aria-label="Previous slide"
      >
        ❮
      </button>
      
      <button 
        className="nav-button next" 
        onClick={() => changeSlide(1)}
        aria-label="Next slide"
      >
        ❯
      </button>

      <div className="dots-container">
        {images.map((_, index) => (
          <span
            key={index}
            className={`dot ${slideIndex === index + 1 ? 'active' : ''}`}
            onClick={() => goToSlide(index + 1)}
          />
        ))}
      </div>
    </div>
  );
};

export default Banner;
