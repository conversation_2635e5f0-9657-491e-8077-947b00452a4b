import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Navigation from '../components/navigation'
import Page from '../app/page'

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <html>
        <body
          {...{
            'inmaintabuse': "1",
            'monica-id': "ofpnmcalabcbjgholdjcjblkibolbppb",
            'monica-version': "7.9.6"
          }}
        >
          <Navigation />
          <div className="z-10">
            {children}
            <Page/>
          </div>
        </body>
      </html>
    </>
  );
}
